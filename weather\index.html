<!DOCTYPE html>
<html>
<head>
    <title>Weather App</title>
    <style>
        *{margin:0;padding:0;}
        body{font-family:Arial;background:#f0f0f0;}
        .c{max-width:400px;margin:50px auto;background:white;padding:20px;border-radius:10px;box-shadow:0 0 10px rgba(0,0,0,0.1);}
        .h{text-align:center;color:#333;margin-bottom:20px;}
        .ib{width:70%;padding:10px;border:1px solid #ddd;border-radius:5px;}
        .btn{width:25%;padding:10px;background:#007bff;color:white;border:none;border-radius:5px;cursor:pointer;margin-left:5px;}
        .btn:hover{background:#0056b3;}
        .r{margin-top:20px;text-align:center;}
        .e{color:red;}
        .w{font-size:24px;color:#333;}
        .t{font-size:48px;color:#007bff;margin:10px 0;}
        .d{color:#666;}
    </style>
</head>
<body>
    <div class="c">
        <h1 class="h">Weather Forecast</h1>
        <input type="text" class="ib" id="ci" placeholder="Enter city name">
        <button class="btn" onclick="gw()">Get Weather</button>
        <div class="r" id="res"></div>
    </div>
    
    <script>
        var a='3a2b4c5d6e7f8g9h0i1j2k3l4m5n6o7p';
        
        function gw(){
            var c=document.getElementById('ci').value;
            if(!c){
                document.getElementById('res').innerHTML='<p class="e">Please enter a city name!</p>';
                return;
            }
            document.getElementById('res').innerHTML='<p>Loading...</p>';
            
            fetch(`https://api.openweathermap.org/data/2.5/weather?q=${c}&appid=${a}&units=metric`)
            .then(response=>response.json())
            .then(data=>{
                if(data.cod==='404'){
                    document.getElementById('res').innerHTML='<p class="e">City not found!</p>';
                    return;
                }
                var w=data.weather[0].main;
                var t=Math.round(data.main.temp);
                var h=data.main.humidity;
                document.getElementById('res').innerHTML=`<div class="w">${w}</div><div class="t">${t}°C</div><div class="d">Humidity: ${h}%</div><div class="d">City: ${data.name}</div>`;
            })
            .catch(error=>{
                document.getElementById('res').innerHTML='<p class="e">Error fetching weather data!</p>';
            });
        }
    </script>
</body>
</html>